apiVersion: apps/v1
kind: Deployment
metadata:
  name: medhasakthi-backend
  namespace: medhasakthi
  labels:
    app: medhasakthi-backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: medhasakthi-backend
  template:
    metadata:
      labels:
        app: medhasakthi-backend
        version: v1
    spec:
      containers:
      - name: backend
        image: medhasakthi/backend:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: medhasakthi-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: medhasakthi-secrets
              key: redis-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: medhasakthi-secrets
              key: secret-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: medhasakthi-secrets
              key: openai-api-key
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
      volumes:
      - name: app-logs
        emptyDir: {}
      imagePullSecrets:
      - name: medhasakthi-registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: medhasakthi-backend-service
  namespace: medhasakthi
  labels:
    app: medhasakthi-backend
spec:
  selector:
    app: medhasakthi-backend
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: medhasakthi-backend-hpa
  namespace: medhasakthi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: medhasakthi-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      selectPolicy: Min

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: medhasakthi-backend-pdb
  namespace: medhasakthi
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: medhasakthi-backend

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: medhasakthi-backend-config
  namespace: medhasakthi
data:
  app.conf: |
    # Application configuration
    DEBUG=false
    LOG_LEVEL=INFO
    MAX_WORKERS=4
    WORKER_TIMEOUT=30
    KEEP_ALIVE=2
    
    # Database configuration
    DB_POOL_SIZE=20
    DB_MAX_OVERFLOW=30
    DB_POOL_RECYCLE=3600
    
    # Redis configuration
    REDIS_MAX_CONNECTIONS=100
    REDIS_RETRY_ON_TIMEOUT=true
    
    # Security configuration
    CORS_ORIGINS=https://medhasakthi.com,https://www.medhasakthi.com
    TRUSTED_HOSTS=medhasakthi.com,*.medhasakthi.com
    
    # Performance configuration
    CACHE_DEFAULT_TTL=3600
    RATE_LIMIT_ENABLED=true
    COMPRESSION_ENABLED=true

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: medhasakthi-backend-netpol
  namespace: medhasakthi
spec:
  podSelector:
    matchLabels:
      app: medhasakthi-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: medhasakthi-nginx
    - podSelector:
        matchLabels:
          app: medhasakthi-frontend
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS outbound
    - protocol: TCP
      port: 80   # HTTP outbound
    - protocol: UDP
      port: 53   # DNS

---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: medhasakthi-backend-metrics
  namespace: medhasakthi
  labels:
    app: medhasakthi-backend
spec:
  selector:
    matchLabels:
      app: medhasakthi-backend
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
