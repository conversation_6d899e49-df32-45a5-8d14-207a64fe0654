{"name": "medha<PERSON>kthi-frontend", "version": "1.0.0", "description": "MEDHASAKTHI - World-Class Educational Technology Platform Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "@mui/material": "^5.12.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.126", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "react-router-dom": "^6.10.0", "react-query": "^3.39.3", "axios": "^1.3.4", "socket.io-client": "^4.6.1", "@reduxjs/toolkit": "^1.9.3", "react-redux": "^8.0.5", "redux-persist": "^6.0.0", "formik": "^2.2.9", "yup": "^1.0.2", "recharts": "^2.5.0", "react-chartjs-2": "^5.2.0", "chart.js": "^4.2.1", "date-fns": "^2.29.3", "lodash": "^4.17.21", "uuid": "^9.0.0", "react-hot-toast": "^2.4.0", "react-loading-skeleton": "^3.2.0", "react-helmet-async": "^1.3.0", "framer-motion": "^10.10.0", "react-spring": "^9.7.1", "react-pdf": "^6.2.2", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "react-webcam": "^7.0.1", "react-speech-recognition": "^3.10.0", "workbox-background-sync": "^6.5.4", "workbox-broadcast-update": "^6.5.4", "workbox-cacheable-response": "^6.5.4", "workbox-core": "^6.5.4", "workbox-expiration": "^6.5.4", "workbox-google-analytics": "^6.5.4", "workbox-navigation-preload": "^6.5.4", "workbox-precaching": "^6.5.4", "workbox-range-requests": "^6.5.4", "workbox-routing": "^6.5.4", "workbox-strategies": "^6.5.4", "workbox-streams": "^6.5.4"}, "devDependencies": {"@types/lodash": "^4.14.194", "@types/uuid": "^9.0.1", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.7", "husky": "^8.0.3", "lint-staged": "^13.2.1", "source-map-explorer": "^2.5.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx source-map-explorer 'build/static/js/*.js'", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "rules": {"no-console": "warn", "no-debugger": "warn", "@typescript-eslint/no-unused-vars": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{css,md}": ["prettier --write"]}, "proxy": "http://localhost:8000", "homepage": ".", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["education", "e-learning", "talent-exam", "ai-powered", "react", "typescript", "material-ui"], "author": "MEDHASAKTHI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/medhasakthi.git"}, "bugs": {"url": "https://github.com/your-org/medhasakthi/issues"}}