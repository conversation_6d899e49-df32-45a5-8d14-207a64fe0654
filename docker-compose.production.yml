version: '3.8'

services:
  # Load Balancer
  nginx:
    image: nginx:alpine
    container_name: medhasakthi_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend-1
      - backend-2
      - frontend
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Backend Services (Multiple instances for load balancing)
  backend-1:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: medhasakthi_backend_1
    environment:
      - DATABASE_URL=postgresql://medhasakthi_user:${DB_PASSWORD}@postgres:5432/medhasakthi_prod
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    depends_on:
      - postgres
      - redis
    networks:
      - medhasakthi_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend-2:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: medhasakthi_backend_2
    environment:
      - DATABASE_URL=postgresql://medhasakthi_user:${DB_PASSWORD}@postgres:5432/medhasakthi_prod
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    depends_on:
      - postgres
      - redis
    networks:
      - medhasakthi_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: medhasakthi_frontend
    environment:
      - REACT_APP_API_URL=https://api.medhasakthi.com
      - REACT_APP_WS_URL=wss://api.medhasakthi.com/ws
      - REACT_APP_ENVIRONMENT=production
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Database
  postgres:
    image: postgres:15-alpine
    container_name: medhasakthi_postgres
    environment:
      - POSTGRES_DB=medhasakthi_prod
      - POSTGRES_USER=medhasakthi_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - medhasakthi_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U medhasakthi_user -d medhasakthi_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: medhasakthi_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - medhasakthi_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for background tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: medhasakthi_celery_worker
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=postgresql://medhasakthi_user:${DB_PASSWORD}@postgres:5432/medhasakthi_prod
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: medhasakthi_celery_beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://medhasakthi_user:${DB_PASSWORD}@postgres:5432/medhasakthi_prod
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: medhasakthi_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: medhasakthi_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # ElasticSearch for logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: medhasakthi_elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: medhasakthi_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: medhasakthi_logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./logging/logs:/logs
    depends_on:
      - elasticsearch
    networks:
      - medhasakthi_network
    restart: unless-stopped

  # Backup service
  backup:
    build:
      context: ./backup
      dockerfile: Dockerfile
    container_name: medhasakthi_backup
    environment:
      - DATABASE_URL=postgresql://medhasakthi_user:${DB_PASSWORD}@postgres:5432/medhasakthi_prod
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BACKUP_BUCKET=${S3_BACKUP_BUCKET}
    volumes:
      - ./database/backups:/backups
      - ./backup/scripts:/scripts
    depends_on:
      - postgres
    networks:
      - medhasakthi_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  medhasakthi_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
