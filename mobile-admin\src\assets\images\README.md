# Logo Assets

## Required Logo Files

Please add the following logo files to this directory:

### 1. medhasakthi.png
- **Size**: 64x64 pixels (or higher resolution with 1:1 aspect ratio)
- **Format**: PNG with transparent background
- **Usage**: Top app bar logo
- **Description**: MEDHASAKTHI logo for use in the student app bar

### 2. medhasakthi-logo-large.png (Future use)
- **Size**: 256x256 pixels (or higher resolution with 1:1 aspect ratio)
- **Format**: PNG with transparent background
- **Usage**: Splash screen, login screen, about page
- **Description**: Large version of the MEDHASAKTHI logo

## Logo Guidelines

- Use PNG format for best quality and transparency support
- Maintain the golden color scheme from the original logo
- Ensure the shield and brain/circuit design are clearly visible even at small sizes
- For the small version, consider simplifying details if needed for clarity at 20x20 display size

## Current Implementation

The StudentTopAppBar component is already configured to use `medhasakthi.png` and will display it at 20x20 pixels within a 28x28 circular container.
