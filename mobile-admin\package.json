{"name": "medhasakthi-mobile-admin", "version": "1.0.0", "description": "MEDHASAKTHI Super Admin Mobile App", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "./build-android.sh", "build:ios": "./build-ios.sh", "build:both": "npm run build:android && npm run build:ios", "clean": "react-native clean-project-auto", "pods": "cd ios && pod install", "postinstall": "cd ios && pod install && cd ..", "reset": "npx react-native start --reset-cache", "generate:icons": "./generate-app-icons.sh", "validate": "./validate-mobile-app.sh", "setup:complete": "npm install && npm run generate:icons && npm run validate"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.25.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "@react-native-async-storage/async-storage": "^1.19.5", "axios": "^1.5.1", "@tanstack/react-query": "^4.36.1", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^13.14.0", "react-native-svg-charts": "^5.4.0", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-device-info": "^10.11.0", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-paper": "^5.11.1", "react-native-image-picker": "^7.0.3", "react-native-document-picker": "^9.1.1", "react-native-share": "^9.4.1", "react-native-push-notification": "^8.1.1", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-native-firebase/analytics": "^18.6.1", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-native-super-grid": "^4.9.6", "react-native-chart-kit": "^6.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@types/lodash": "^4.14.200", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}