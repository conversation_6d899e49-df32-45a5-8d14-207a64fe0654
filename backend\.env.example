# MEDHASAKTHI Backend Environment Configuration
# Copy this file to .env and update the values

# Application Settings
APP_NAME=MEDHASAKTHI API
APP_VERSION=1.0.0
DEBUG=true

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production-make-it-very-long-and-random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Configuration
DATABASE_URL=postgresql://admin:password@localhost:5432/medhasakthi
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=MEDHASAKTHI
SMTP_USE_TLS=true

# Email Configuration (SendGrid - Alternative)
SENDGRID_API_KEY=your-sendgrid-api-key-here
FROM_EMAIL=<EMAIL>
FROM_NAME=MEDHASAKTHI

# Frontend URLs
FRONTEND_URL=http://localhost:3000

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,https://medhasakthi.vercel.app

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# UPI Payment Configuration
UPI_ENABLED=true
UPI_PRIMARY_ID=medhasakthi@paytm
UPI_SECONDARY_ID=medhasakthi@phonepe
UPI_MIN_AMOUNT=10
UPI_MAX_AMOUNT=50000
UPI_PAYMENT_TIMEOUT_MINUTES=15

# Payment Gateway Configuration
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# AI Services
OPENAI_API_KEY=your-openai-api-key-here
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# Sentry Configuration (Error Tracking)
SENTRY_DSN=your-sentry-dsn-here
ENVIRONMENT=production

# Two-Factor Authentication
TOTP_ISSUER=MEDHASAKTHI
BACKUP_CODES_COUNT=10

# SMS Service Configuration (for 2FA)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Advanced Security
THREAT_DETECTION_ENABLED=true
INTRUSION_DETECTION_ENABLED=true
ADVANCED_RATE_LIMITING=true
IP_WHITELIST_ENABLED=false

# Business Intelligence
BI_ENABLED=true
AI_INSIGHTS_ENABLED=true
PREDICTIVE_ANALYTICS_ENABLED=true

# CSRF Protection
CSRF_SECRET_KEY=your-csrf-secret-key-here

# Backup Configuration
BACKUP_DIR=/app/backups
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key-here
USE_CLOUD_BACKUP=false
BACKUP_S3_BUCKET=your-s3-bucket-name
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# Exam Security
EXAM_SESSION_TIMEOUT_MINUTES=180
MAX_CONCURRENT_SESSIONS=1

# Monitoring
SENTRY_DSN=your-sentry-dsn-here

# Development Settings
LOG_LEVEL=INFO
ENABLE_DOCS=true
