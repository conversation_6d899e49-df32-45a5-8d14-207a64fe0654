# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2
pyotp==2.9.0

# Redis for caching and sessions
redis==5.0.1
aioredis==2.0.1

# Email
sendgrid==6.10.0
jinja2==3.1.2

# Environment and configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client
httpx==0.25.2
aiohttp==3.9.1
aiofiles==23.2.1

# Validation and utilities
email-validator==2.1.0
phonenumbers==8.13.26

# AI/ML (for future integration)
openai==1.3.7
transformers==4.35.2
torch==2.1.1
numpy==1.25.2
pandas==2.1.4

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Monitoring and logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# CORS
fastapi-cors==0.0.6

# Rate limiting
slowapi==0.1.9

# Background tasks
celery==5.3.4
