# 🚀 MEDHASAKTHI Deployment - Next Steps

**Your GitHub repository**: https://github.com/Medhasakthi/MEDHASAKTHI.git  
**DNS Configuration**: ✅ Completed

## 📋 Current Status
- ✅ GitHub repository ready
- ✅ Domain DNS configured to point to EC2
- 🔄 **Next**: EC2 server setup and deployment

## 🎯 Step-by-Step Deployment Process

### Step 1: Launch and Connect to EC2 Instance

1. **Launch EC2 Instance** with these specifications:
   ```
   Instance Type: t3.large (minimum) or t3.xlarge (recommended)
   AMI: Ubuntu Server 22.04 LTS
   Storage: 50GB+ GP3 SSD
   ```

2. **Configure Security Group** with these inbound rules:
   ```
   SSH (22) - Your IP address
   HTTP (80) - 0.0.0.0/0
   HTTPS (443) - 0.0.0.0/0
   Custom TCP (3000) - 0.0.0.0/0  # Grafana monitoring
   Custom TCP (9090) - 0.0.0.0/0  # Prometheus metrics
   Custom TCP (5601) - 0.0.0.0/0  # <PERSON><PERSON> logs
   ```

3. **Connect to your EC2 instance**:
   ```bash
   ssh -i your-key-pair.pem ubuntu@your-ec2-public-ip
   ```

### Step 2: Run Automated Server Setup

Execute this single command to set up everything:

```bash
# Download and run the setup script
wget https://raw.githubusercontent.com/Medhasakthi/MEDHASAKTHI/main/setup-ec2-server.sh
chmod +x setup-ec2-server.sh
DOMAIN=medhasakthi.com EMAIL=<EMAIL> ./setup-ec2-server.sh
```

**What this script does:**
- ✅ Updates system packages
- ✅ Installs Docker and Docker Compose
- ✅ Installs AWS CLI and essential tools
- ✅ Clones your MEDHASAKTHI repository
- ✅ Creates application directories
- ✅ Generates secure environment configuration
- ✅ Sets up deployment and health check scripts
- ✅ Configures automated backup system

**Expected output**: The script will show green checkmarks ✅ for each completed step.

### Step 3: Log Out and Back In

```bash
# Log out to apply Docker group changes
exit

# Log back in
ssh -i your-key-pair.pem ubuntu@your-ec2-public-ip
```

### Step 4: Generate SSL Certificate

```bash
# Generate SSL certificate for your domain
sudo certbot --nginx -d medhasakthi.com -d www.medhasakthi.com

# Follow the prompts:
# - Enter email address
# - Agree to terms of service
# - Choose whether to share email with EFF
```

### Step 5: Configure Environment Variables

```bash
# Navigate to application directory
cd /opt/medhasakthi

# Edit environment configuration
nano .env
```

**Update these important variables in .env file:**
```env
# Email Configuration (for notifications and user registration)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password  # Use App Password for Gmail

# AWS Configuration (optional, for S3 storage and backups)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1
S3_BUCKET_NAME=medhasakthi-storage
S3_BACKUP_BUCKET=medhasakthi-backups

# OpenAI Configuration (optional, for AI features)
OPENAI_API_KEY=your_openai_api_key

# Domain (should already be correct)
DOMAIN=medhasakthi.com
EMAIL=<EMAIL>
```

### Step 6: Initial Application Deployment

```bash
# Deploy the application
./deploy.sh
```

**This will:**
- ✅ Pull latest code from GitHub
- ✅ Build Docker containers
- ✅ Start all services (database, backend, frontend, monitoring)
- ✅ Run database migrations
- ✅ Perform health checks

**Expected time**: 5-10 minutes for first deployment

### Step 7: Verify Deployment

```bash
# Run comprehensive health check
./health-check.sh
```

**Check these URLs in your browser:**
- **Main Application**: https://medhasakthi.com
- **API Documentation**: https://medhasakthi.com/api/docs
- **Health Check**: https://medhasakthi.com/health

### Step 8: Setup GitHub Actions for Automated Deployment

1. **Generate SSH Key for GitHub Actions**:
   ```bash
   # On your local machine (not EC2)
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/medhasakthi_deploy
   
   # Copy public key to EC2 server
   ssh-copy-id -i ~/.ssh/medhasakthi_deploy.pub ubuntu@your-ec2-public-ip
   ```

2. **Add GitHub Secrets**:
   - Go to: https://github.com/Medhasakthi/MEDHASAKTHI/settings/secrets/actions
   - Click "New repository secret"
   - Add these secrets:

   ```
   Name: PRODUCTION_HOST
   Value: your-ec2-public-ip

   Name: PRODUCTION_USER
   Value: ubuntu

   Name: PRODUCTION_SSH_KEY
   Value: [paste the content of ~/.ssh/medhasakthi_deploy private key file]
   ```

3. **Test Automated Deployment**:
   ```bash
   # Make a small change and push to trigger deployment
   git add .
   git commit -m "Test automated deployment"
   git push origin main
   ```

## 🎉 Deployment Complete!

### Your Application URLs:
- **🏠 Main Site**: https://medhasakthi.com
- **👨‍💼 Admin Panel**: https://medhasakthi.com/admin
- **👨‍🎓 Student Portal**: https://medhasakthi.com/student
- **🏫 Institute Portal**: https://medhasakthi.com/institute
- **📚 API Docs**: https://medhasakthi.com/api/docs

### Monitoring Dashboards:
- **📊 Grafana**: https://medhasakthi.com:3000
- **📈 Prometheus**: https://medhasakthi.com:9090
- **📋 Kibana**: https://medhasakthi.com:5601

### Default Login Credentials:
```
Super Admin:
Email: <EMAIL>
Password: ChangeMe123!

Grafana:
Username: admin
Password: [check .env file for GRAFANA_PASSWORD]
```

## 🔧 Daily Operations

### Useful Commands:
```bash
# Check system health
./health-check.sh

# Manual deployment
./deploy.sh

# View application logs
docker-compose -f docker-compose.production.yml logs --tail=100

# Restart specific service
docker-compose -f docker-compose.production.yml restart backend

# Manual backup
sudo /usr/local/bin/medhasakthi-backup.sh

# Check running services
docker-compose -f docker-compose.production.yml ps
```

### Automated Features:
- ✅ **Daily Backups**: Automatic database backup at 2 AM
- ✅ **SSL Renewal**: Automatic certificate renewal
- ✅ **Health Monitoring**: Continuous service monitoring
- ✅ **Auto Deployment**: Deploy on git push to main branch

## 🚨 Troubleshooting

### If deployment fails:
```bash
# Check service status
docker-compose -f docker-compose.production.yml ps

# Check logs for errors
docker-compose -f docker-compose.production.yml logs

# Restart all services
docker-compose -f docker-compose.production.yml restart

# Full restart (if needed)
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d
```

### If SSL certificate fails:
```bash
# Check if domain points to correct IP
nslookup medhasakthi.com

# Retry certificate generation
sudo certbot delete --cert-name medhasakthi.com
sudo certbot --nginx -d medhasakthi.com -d www.medhasakthi.com
```

### If GitHub Actions deployment fails:
- Verify SSH key is correctly added to GitHub secrets
- Check that EC2 security group allows SSH access
- Ensure deployment script has execute permissions

## 📞 Support

### Getting Help:
1. **Check Health**: Run `./health-check.sh`
2. **Check Logs**: `docker-compose -f docker-compose.production.yml logs`
3. **GitHub Issues**: Report issues at https://github.com/Medhasakthi/MEDHASAKTHI/issues

### Emergency Contacts:
- **System Status**: `./health-check.sh`
- **Emergency Restart**: `docker-compose -f docker-compose.production.yml restart`
- **Emergency Backup**: `sudo /usr/local/bin/medhasakthi-backup.sh`

## 🎯 Next Steps After Deployment

1. **✅ Change Default Passwords**: Update admin and Grafana passwords
2. **✅ Configure Email Settings**: Set up SMTP for notifications
3. **✅ Add Content**: Upload educational content and courses
4. **✅ Test Features**: Test all functionality thoroughly
5. **✅ Set Up Monitoring Alerts**: Configure Grafana alerts
6. **✅ Performance Testing**: Run load tests
7. **✅ User Training**: Train administrators and teachers

## 🏆 Congratulations!

Your MEDHASAKTHI educational platform is now live with:
- ✅ **Enterprise-grade infrastructure**
- ✅ **Automated CI/CD pipeline**
- ✅ **SSL security and monitoring**
- ✅ **Scalable architecture**
- ✅ **Production-ready features**

Ready to serve students and institutions worldwide! 🌍📚
