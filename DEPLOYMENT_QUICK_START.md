# 🚀 MEDHASAKTHI Quick Deployment Guide

**Deploy MEDHASAKTH<PERSON> from GitHub to AWS EC2 in 30 minutes!**

## 🎯 Overview

This guide will help you deploy your MEDHASAKTHI educational platform from GitHub to AWS EC2 with automated CI/CD pipeline. The deployment includes:

- ✅ **Automated GitHub Actions CI/CD**
- ✅ **SSL certificates with auto-renewal**
- ✅ **Load balancing and monitoring**
- ✅ **Database backups and health checks**
- ✅ **Production-ready configuration**

## 📋 Prerequisites

### 1. AWS Account
- AWS account with EC2 access
- Basic understanding of AWS services

### 2. Domain Name
- Domain name (e.g., medhasakthi.com)
- Access to DNS management

### 3. GitHub Repository
- GitHub account
- MEDHASAKTHI repository (fork or clone)

## 🚀 Quick Start (30 Minutes)

### Step 1: Launch EC2 Instance (5 minutes)

1. **Launch EC2 Instance**:
   ```
   Instance Type: t3.large (minimum) or t3.xlarge (recommended)
   OS: Ubuntu 22.04 LTS
   Storage: 50GB+ SSD
   ```

2. **Configure Security Group**:
   ```
   SSH (22) - Your IP
   HTTP (80) - 0.0.0.0/0
   HTTPS (443) - 0.0.0.0/0
   Custom TCP (3000) - 0.0.0.0/0  # Grafana
   Custom TCP (9090) - 0.0.0.0/0  # Prometheus
   ```

3. **Download Key Pair** and save it securely

### Step 2: Connect and Setup Server (10 minutes)

1. **Connect to EC2**:
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-public-ip
   ```

2. **Download and Run Setup Script**:
   ```bash
   # Download setup script
   wget https://raw.githubusercontent.com/Medhasakthi/MEDHASAKTHI/main/setup-ec2-server.sh
   
   # Make executable
   chmod +x setup-ec2-server.sh
   
   # Run setup (replace with your domain and email)
   DOMAIN=medhasakthi.com EMAIL=<EMAIL> ./setup-ec2-server.sh
   ```

3. **Log out and log back in** for Docker group changes:
   ```bash
   exit
   ssh -i your-key.pem ubuntu@your-ec2-public-ip
   ```

### Step 3: Configure Domain and SSL (5 minutes)

1. **Update DNS Records**:
   ```
   A Record: medhasakthi.com -> YOUR_EC2_PUBLIC_IP
   A Record: www.medhasakthi.com -> YOUR_EC2_PUBLIC_IP
   ```

2. **Generate SSL Certificate**:
   ```bash
   sudo certbot --nginx -d medhasakthi.com -d www.medhasakthi.com
   ```

### Step 4: Configure GitHub Actions (5 minutes)

1. **Generate SSH Key for GitHub Actions**:
   ```bash
   # On your local machine
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/medhasakthi_deploy
   
   # Copy public key to EC2
   ssh-copy-id -i ~/.ssh/medhasakthi_deploy.pub ubuntu@your-ec2-ip
   ```

2. **Add GitHub Secrets**:
   Go to your GitHub repository → Settings → Secrets and variables → Actions
   
   Add these secrets:
   ```
   PRODUCTION_HOST: your-ec2-public-ip
   PRODUCTION_USER: ubuntu
   PRODUCTION_SSH_KEY: [content of ~/.ssh/medhasakthi_deploy private key]
   ```

### Step 5: Deploy Application (5 minutes)

1. **Update Environment Configuration**:
   ```bash
   cd /opt/medhasakthi
   nano .env  # Update with your specific settings
   ```

2. **Initial Deployment**:
   ```bash
   ./deploy.sh
   ```

3. **Verify Deployment**:
   ```bash
   ./health-check.sh
   ```

## 🎉 Success! Your Application is Live

Visit your application:
- **Main Site**: https://medhasakthi.com
- **API Docs**: https://medhasakthi.com/api/docs
- **Grafana**: https://medhasakthi.com:3000
- **Prometheus**: https://medhasakthi.com:9090

## 🔄 Automated Deployment

Now every time you push to the `main` branch, GitHub Actions will automatically:

1. ✅ Run tests
2. ✅ Build Docker images
3. ✅ Deploy to your EC2 instance
4. ✅ Run health checks
5. ✅ Notify you of deployment status

## 📊 Monitoring and Maintenance

### Daily Automated Tasks
- **Database Backups**: Automatic daily backups at 2 AM
- **SSL Renewal**: Automatic certificate renewal
- **Health Checks**: Continuous monitoring

### Manual Commands
```bash
# Check system health
./health-check.sh

# Manual deployment
./deploy.sh

# Manual backup
sudo /usr/local/bin/medhasakthi-backup.sh

# View logs
docker-compose -f docker-compose.production.yml logs --tail=100

# Restart services
docker-compose -f docker-compose.production.yml restart
```

## 🔧 Troubleshooting

### Common Issues

1. **SSL Certificate Issues**:
   ```bash
   sudo certbot delete --cert-name medhasakthi.com
   sudo certbot --nginx -d medhasakthi.com -d www.medhasakthi.com
   ```

2. **GitHub Actions Deployment Fails**:
   - Check SSH key is correctly added to GitHub secrets
   - Verify EC2 security group allows SSH from GitHub Actions IPs
   - Check deployment script permissions: `ls -la /opt/medhasakthi/deploy.sh`

3. **Application Not Accessible**:
   ```bash
   # Check if services are running
   docker-compose -f docker-compose.production.yml ps
   
   # Check nginx status
   sudo systemctl status nginx
   
   # Check logs
   docker-compose -f docker-compose.production.yml logs
   ```

4. **Database Connection Issues**:
   ```bash
   # Restart database
   docker-compose -f docker-compose.production.yml restart postgres
   
   # Check database logs
   docker-compose -f docker-compose.production.yml logs postgres
   ```

## 📈 Scaling Your Deployment

### Performance Optimization
- **Enable CDN**: Set up CloudFront for static assets
- **Database Optimization**: Configure connection pooling
- **Caching**: Implement Redis caching strategies
- **Load Balancing**: Add multiple EC2 instances behind ALB

### Security Enhancements
- **WAF**: Configure AWS WAF for additional protection
- **VPC**: Move to private subnets with NAT Gateway
- **Secrets Management**: Use AWS Secrets Manager
- **Monitoring**: Set up CloudWatch alarms

## 🆘 Support

### Getting Help
- **Documentation**: Check the comprehensive guides in the repository
- **Health Check**: Run `./health-check.sh` for system status
- **Logs**: Check application logs for specific errors
- **GitHub Issues**: Report issues in the repository

### Emergency Procedures
```bash
# Emergency restart
docker-compose -f docker-compose.production.yml restart

# Emergency rollback (if needed)
git checkout previous-working-commit
./deploy.sh

# Emergency backup
sudo /usr/local/bin/medhasakthi-backup.sh
```

## 🎯 Next Steps

1. **Configure Monitoring Alerts**: Set up Grafana alerts
2. **Customize Application**: Update branding and configuration
3. **Add Users**: Create admin and test accounts
4. **Performance Testing**: Run load tests
5. **Backup Strategy**: Configure S3 backup storage
6. **Documentation**: Update with your specific configurations

## 🏆 Congratulations!

You've successfully deployed MEDHASAKTHI with:
- ✅ **Production-ready infrastructure**
- ✅ **Automated CI/CD pipeline**
- ✅ **SSL security and monitoring**
- ✅ **Scalable architecture**
- ✅ **Automated backups and health checks**

Your educational platform is now ready to serve students and institutions worldwide! 🌍📚

---

**Need help?** Check the detailed [GitHub to EC2 Deployment Guide](GITHUB_TO_EC2_DEPLOYMENT_GUIDE.md) for comprehensive instructions.
