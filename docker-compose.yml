# MEDHASAKTHI Docker Compose Configuration
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medhasakthi-postgres
    environment:
      POSTGRES_DB: medhasakthi
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_me}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - /app/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d medhasakthi"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: medhasakthi-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_change_me}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medhasakthi-backend
    environment:
      - DATABASE_URL=postgresql://admin:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/medhasakthi
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-this-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CSRF_SECRET_KEY=${CSRF_SECRET_KEY}
      - BACKUP_ENCRYPTION_KEY=${BACKUP_ENCRYPTION_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - DEBUG=false
      - ENVIRONMENT=production
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
      - UPI_ENABLED=${UPI_ENABLED}
      - UPI_PRIMARY_ID=${UPI_PRIMARY_ID}
      - FRONTEND_URL=https://${DOMAIN:-medhasakthi.com}
      - BACKEND_CORS_ORIGINS=https://${DOMAIN:-medhasakthi.com}
    volumes:
      - ./backend/uploads:/app/uploads
      - /app/logs:/app/logs
      - /app/backups:/app/backups
      - /app/certificates:/app/certificates
      - /app/static:/app/static
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Frontend Application (React)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medhasakthi-frontend
    environment:
      - REACT_APP_API_URL=https://api.medhasakthi.com
      - REACT_APP_APP_NAME=MEDHASAKTHI
      - NODE_ENV=production
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: medhasakthi-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: medhasakthi-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - medhasakthi-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: medhasakthi-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3003:3000"
    depends_on:
      - prometheus
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  medhasakthi-network:
    driver: bridge
