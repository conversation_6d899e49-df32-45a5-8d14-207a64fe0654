version: '3.8'

# MEDHASAKTHI Load Balanced Architecture
# For 2000+ concurrent users

services:
  # Load Balancer (Nginx)
  loadbalancer:
    image: nginx:alpine
    container_name: medhasakthi-loadbalancer
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-loadbalancer.conf:/etc/nginx/nginx.conf
      - ./certificates:/etc/nginx/ssl
    depends_on:
      - backend-1
      - backend-2
      - backend-3
      - frontend-1
      - frontend-2
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Frontend Instances (2 replicas)
  frontend-1:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medhasakthi-frontend-1
    environment:
      - REACT_APP_API_URL=https://${DOMAIN:-medhasakthi.com}/api
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  frontend-2:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medhasakthi-frontend-2
    environment:
      - REACT_APP_API_URL=https://${DOMAIN:-medhasakthi.com}/api
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Backend Instances (3 replicas)
  backend-1:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medhasakthi-backend-1
    environment:
      - DATABASE_URL=postgresql://admin:${POSTGRES_PASSWORD}@postgres-primary:5432/medhasakthi
      - DATABASE_READ_URL=postgresql://admin:${POSTGRES_PASSWORD}@postgres-replica:5432/medhasakthi
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis-cluster:6379
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-1
    volumes:
      - /app/logs:/app/logs
      - /app/uploads:/app/uploads
    depends_on:
      - postgres-primary
      - redis-cluster
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  backend-2:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medhasakthi-backend-2
    environment:
      - DATABASE_URL=postgresql://admin:${POSTGRES_PASSWORD}@postgres-primary:5432/medhasakthi
      - DATABASE_READ_URL=postgresql://admin:${POSTGRES_PASSWORD}@postgres-replica:5432/medhasakthi
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis-cluster:6379
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-2
    volumes:
      - /app/logs:/app/logs
      - /app/uploads:/app/uploads
    depends_on:
      - postgres-primary
      - redis-cluster
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  backend-3:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medhasakthi-backend-3
    environment:
      - DATABASE_URL=postgresql://admin:${POSTGRES_PASSWORD}@postgres-primary:5432/medhasakthi
      - DATABASE_READ_URL=postgresql://admin:${POSTGRES_PASSWORD}@postgres-replica:5432/medhasakthi
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis-cluster:6379
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-3
    volumes:
      - /app/logs:/app/logs
      - /app/uploads:/app/uploads
    depends_on:
      - postgres-primary
      - redis-cluster
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # Database Primary (Master)
  postgres-primary:
    image: postgres:15-alpine
    container_name: medhasakthi-postgres-primary
    environment:
      POSTGRES_DB: medhasakthi
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_REPLICATION_MODE: master
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'

  # Database Replica (Read-only)
  postgres-replica:
    image: postgres:15-alpine
    container_name: medhasakthi-postgres-replica
    environment:
      POSTGRES_MASTER_SERVICE: postgres-primary
      POSTGRES_REPLICATION_MODE: slave
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD}
      POSTGRES_MASTER_PORT_NUMBER: 5432
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
    depends_on:
      - postgres-primary
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'

  # Redis Cluster
  redis-cluster:
    image: redis:7-alpine
    container_name: medhasakthi-redis-cluster
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_cluster_data:/data
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: medhasakthi-prometheus-lb
    volumes:
      - ./monitoring/prometheus-cluster.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  grafana:
    image: grafana/grafana:latest
    container_name: medhasakthi-grafana-lb
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Log Aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: medhasakthi-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: medhasakthi-logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - /app/logs:/app/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: medhasakthi-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - medhasakthi-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

volumes:
  postgres_primary_data:
  postgres_replica_data:
  redis_cluster_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  medhasakthi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
