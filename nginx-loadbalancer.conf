# MEDHASAKTHI Load Balancer Configuration
# Nginx configuration for high-availability setup

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimize worker connections
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=register:10m rate=3r/m;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    # Backend upstream pools
    upstream backend_pool {
        least_conn;  # Load balancing method
        
        server backend-1:8000 max_fails=3 fail_timeout=30s weight=1;
        server backend-2:8000 max_fails=3 fail_timeout=30s weight=1;
        server backend-3:8000 max_fails=3 fail_timeout=30s weight=1;
        
        # Health check
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }

    upstream frontend_pool {
        least_conn;
        
        server frontend-1:3000 max_fails=2 fail_timeout=30s weight=1;
        server frontend-2:3000 max_fails=2 fail_timeout=30s weight=1;
        
        keepalive 16;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' wss: https:;" always;

    # Hide nginx version
    server_tokens off;

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name medhasakthi.com www.medhasakthi.com;
        
        # Security headers even for redirects
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        
        return 301 https://$server_name$request_uri;
    }

    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name medhasakthi.com www.medhasakthi.com;

        # SSL certificates
        ssl_certificate /etc/nginx/ssl/medhasakthi.crt;
        ssl_certificate_key /etc/nginx/ssl/medhasakthi.key;

        # Connection limits
        limit_conn perip 20;
        limit_conn perserver 1000;

        # Frontend routes
        location / {
            proxy_pass http://frontend_pool;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffering
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                proxy_pass http://frontend_pool;
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header X-Cache-Status "STATIC";
            }
        }

        # API routes with rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend_pool;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            
            # Timeouts for API calls
            proxy_connect_timeout 10s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # Disable buffering for real-time responses
            proxy_buffering off;
            proxy_request_buffering off;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Authentication endpoints with stricter rate limiting
        location /api/auth/login {
            limit_req zone=login burst=3 nodelay;
            
            proxy_pass http://backend_pool;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Shorter timeouts for auth
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        location /api/auth/register {
            limit_req zone=register burst=2 nodelay;
            
            proxy_pass http://backend_pool;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket connections
        location /api/v1/ws/ {
            proxy_pass http://backend_pool;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific timeouts
            proxy_connect_timeout 7s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        # File uploads
        location /api/upload/ {
            client_max_body_size 100M;
            
            proxy_pass http://backend_pool;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for file uploads
            proxy_connect_timeout 10s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
            
            # Disable request buffering for large files
            proxy_request_buffering off;
        }

        # Health check endpoint
        location /health {
            access_log off;
            
            proxy_pass http://backend_pool;
            proxy_set_header Host $host;
            proxy_connect_timeout 2s;
            proxy_send_timeout 5s;
            proxy_read_timeout 5s;
        }

        # Nginx status (internal monitoring)
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }

        # Block common attack patterns
        location ~* \.(php|asp|aspx|jsp)$ {
            return 444;
        }

        location ~* /(wp-admin|wp-login|admin|phpmyadmin) {
            return 444;
        }

        # Security.txt
        location /.well-known/security.txt {
            return 200 "Contact: <EMAIL>\nExpires: 2024-12-31T23:59:59.000Z\nPreferred-Languages: en\n";
            add_header Content-Type text/plain;
        }
    }

    # Monitoring endpoints (restricted access)
    server {
        listen 9090 ssl;
        server_name medhasakthi.com;
        
        ssl_certificate /etc/nginx/ssl/medhasakthi.crt;
        ssl_certificate_key /etc/nginx/ssl/medhasakthi.key;
        
        # Restrict access to monitoring
        allow 127.0.0.1;
        allow **********/16;  # Docker network
        deny all;
        
        location / {
            proxy_pass http://prometheus:9090;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    server {
        listen 3001 ssl;
        server_name medhasakthi.com;
        
        ssl_certificate /etc/nginx/ssl/medhasakthi.crt;
        ssl_certificate_key /etc/nginx/ssl/medhasakthi.key;
        
        location / {
            proxy_pass http://grafana:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
