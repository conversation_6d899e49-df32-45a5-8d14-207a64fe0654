# MEDHASAKTHI Production Environment Configuration
# Copy this file to .env and update all values for production deployment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME=MEDHASAKTHI API
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO

# =============================================================================
# SECURITY CONFIGURATION (CRITICAL - MUST CHANGE)
# =============================================================================
# Generate strong secret keys using: openssl rand -hex 32
SECRET_KEY=CHANGE_THIS_TO_STRONG_SECRET_KEY_32_CHARS_MINIMUM
JWT_SECRET_KEY=CHANGE_THIS_TO_STRONG_JWT_SECRET_KEY
CSRF_SECRET_KEY=CHANGE_THIS_TO_STRONG_CSRF_SECRET_KEY
BACKUP_ENCRYPTION_KEY=CHANGE_THIS_TO_STRONG_BACKUP_KEY

# JWT Configuration
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Production PostgreSQL Database
DATABASE_URL=postgresql://medhasakthi_user:CHANGE_DB_PASSWORD@localhost:5432/medhasakthi_prod
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Database Credentials
POSTGRES_DB=medhasakthi_prod
POSTGRES_USER=medhasakthi_user
POSTGRES_PASSWORD=CHANGE_DB_PASSWORD

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=CHANGE_REDIS_PASSWORD

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
DOMAIN=medhasakthi.com
FRONTEND_URL=https://medhasakthi.com
BACKEND_URL=https://api.medhasakthi.com
ADMIN_URL=https://admin.medhasakthi.com
STUDENT_URL=https://student.medhasakthi.com
TEACHER_URL=https://teacher.medhasakthi.com
LEARN_URL=https://learn.medhasakthi.com

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=https://medhasakthi.com,https://www.medhasakthi.com,https://admin.medhasakthi.com,https://student.medhasakthi.com,https://teacher.medhasakthi.com,https://learn.medhasakthi.com

# =============================================================================
# EMAIL CONFIGURATION (Choose one method)
# =============================================================================

# Method 1: SMTP Configuration (Gmail/Custom SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=YOUR_APP_PASSWORD_HERE
EMAIL_USE_TLS=true
FROM_EMAIL=<EMAIL>
FROM_NAME=MEDHASAKTHI

# Method 2: SendGrid (Alternative)
SENDGRID_API_KEY=YOUR_SENDGRID_API_KEY_HERE

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================
# OpenAI API for question generation
OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Hugging Face (Optional)
HUGGINGFACE_API_KEY=YOUR_HUGGINGFACE_API_KEY_HERE

# =============================================================================
# UPI PAYMENT CONFIGURATION
# =============================================================================
UPI_ENABLED=true
UPI_PRIMARY_ID=medhasakthi@paytm
UPI_MERCHANT_NAME=MEDHASAKTHI
UPI_MERCHANT_CODE=MEDHA001

# UPI Provider Configuration
UPI_PHONEPE_ENABLED=true
UPI_GOOGLEPAY_ENABLED=true
UPI_PAYTM_ENABLED=true
UPI_BHIM_ENABLED=true
UPI_AMAZONPAY_ENABLED=true
UPI_WHATSAPP_ENABLED=true

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# Local Storage
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# AWS S3 (Optional for file storage)
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_KEY
AWS_REGION=us-east-1
S3_BUCKET_NAME=medhasakthi-uploads
S3_BACKUP_BUCKET=medhasakthi-backups

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Sentry for error tracking (Optional)
SENTRY_DSN=YOUR_SENTRY_DSN_HERE

# Prometheus monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Grafana dashboard
GRAFANA_ENABLED=true
GRAFANA_PORT=3001
GRAFANA_ADMIN_PASSWORD=CHANGE_GRAFANA_PASSWORD

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================
RATE_LIMIT_PER_MINUTE=60
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=1800

# Advanced Security Features
THREAT_DETECTION_ENABLED=true
INTRUSION_DETECTION_ENABLED=true
ADVANCED_RATE_LIMITING=true
IP_WHITELIST_ENABLED=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_DIR=/app/backups
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
USE_CLOUD_BACKUP=false

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/letsencrypt/live/medhasakthi.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/medhasakthi.com/privkey.pem

# =============================================================================
# BUSINESS INTELLIGENCE & ANALYTICS
# =============================================================================
BI_ENABLED=true
AI_INSIGHTS_ENABLED=true
PREDICTIVE_ANALYTICS_ENABLED=true

# =============================================================================
# EXAM SECURITY CONFIGURATION
# =============================================================================
EXAM_SESSION_TIMEOUT_MINUTES=180
MAX_CONCURRENT_SESSIONS=1
PROCTORING_ENABLED=true
SCREENSHOT_MONITORING=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Real-time notifications
WEBSOCKET_ENABLED=true
PUSH_NOTIFICATIONS_ENABLED=true

# SMS Configuration (Optional for 2FA)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=YOUR_TWILIO_SID
TWILIO_AUTH_TOKEN=YOUR_TWILIO_TOKEN
TWILIO_PHONE_NUMBER=YOUR_TWILIO_PHONE

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
# Caching
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Database optimization
DB_QUERY_TIMEOUT=30
DB_CONNECTION_TIMEOUT=10

# =============================================================================
# DEVELOPMENT/TESTING (Set to false in production)
# =============================================================================
ENABLE_DOCS=false
ENABLE_REDOC=false
ENABLE_SWAGGER_UI=false

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=medhasakthi
DOCKER_RESTART_POLICY=unless-stopped

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
TIMEZONE=Asia/Kolkata
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,hi,ta,te,kn

# =============================================================================
# CONTACT INFORMATION
# =============================================================================
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
CONTACT_PHONE=+91-XXXX-XXXX

# =============================================================================
# LEGAL & COMPLIANCE
# =============================================================================
PRIVACY_POLICY_URL=https://medhasakthi.com/privacy
TERMS_OF_SERVICE_URL=https://medhasakthi.com/terms
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=2555

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_AI_QUESTIONS=true
FEATURE_UPI_PAYMENTS=true
FEATURE_CERTIFICATES=true
FEATURE_ANALYTICS=true
FEATURE_INTEGRATIONS=true
FEATURE_MOBILE_APP=true

# =============================================================================
# MAINTENANCE MODE
# =============================================================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System under maintenance. Please try again later.

# =============================================================================
# NOTES FOR PRODUCTION DEPLOYMENT
# =============================================================================
# 1. Replace ALL "CHANGE_" prefixed values with actual production values
# 2. Generate strong random keys for all secret keys
# 3. Use strong passwords for database and Redis
# 4. Configure proper email service (Gmail App Password or SendGrid)
# 5. Set up OpenAI API key for AI features
# 6. Configure domain and SSL certificates
# 7. Set up monitoring services (Sentry, etc.)
# 8. Test all configurations before going live
# 9. Keep this file secure and never commit to version control
# 10. Create backups of this configuration file
