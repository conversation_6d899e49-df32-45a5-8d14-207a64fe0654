"""
Pydantic schemas for MEDHASAKTHI API
"""
from .auth import (
    UserRegisterSchema,
    UserLoginSchema,
    TokenResponseSchema,
    RefreshTokenSchema,
    PasswordResetRequestSchema,
    PasswordResetSchema,
    PasswordChangeSchema,
    EmailVerificationSchema,
    TwoFactorSetupSchema,
    TwoFactorDisableSchema,
    UserResponseSchema,
    UserProfileResponseSchema,
    UserSessionResponseSchema,
    PasswordStrengthResponseSchema,
    AuthStatusResponseSchema,
    LogoutResponseSchema,
    BulkUserRegistrationSchema,
    UserUpdateSchema,
    DeviceInfoSchema,
    SecurityEventSchema
)
from .question import (
    QuestionGenerationRequestSchema,
    QuestionCreateSchema,
    QuestionUpdateSchema,
    QuestionResponseSchema,
    QuestionGenerationResponseSchema,
    SubjectCreateSchema,
    SubjectResponseSchema,
    TopicCreateSchema,
    TopicResponseSchema,
    QuestionBankCreateSchema,
    QuestionBankResponseSchema,
    QuestionFeedbackSchema,
    QuestionSearchSchema,
    QuestionSearchResponseSchema,
    AIGenerationStatsSchema,
    BulkQuestionImportSchema,
    QuestionValidationSchema,
    QuestionValidationResponseSchema
)
from .certificate import (
    CertificateTemplateCreateSchema,
    CertificateTemplateUpdateSchema,
    CertificateTemplateResponseSchema,
    CertificateCreateSchema,
    CertificateBulkCreateSchema,
    CertificateUpdateSchema,
    CertificateResponseSchema,
    CertificateVerificationSchema,
    CertificateVerificationResponseSchema,
    CertificateGenerationRequestSchema,
    CertificateGenerationResponseSchema,
    CertificateSearchSchema,
    CertificateSearchResponseSchema,
    CertificateStatsSchema
)
from .talent_exam import (
    TalentExamCreateSchema,
    TalentExamUpdateSchema,
    TalentExamResponseSchema,
    TalentExamRegistrationCreateSchema,
    TalentExamRegistrationUpdateSchema,
    TalentExamRegistrationResponseSchema,
    ExamCenterCreateSchema,
    ExamCenterResponseSchema,
    TalentExamSearchSchema,
    TalentExamSearchResponseSchema,
    TalentExamNotificationCreateSchema,
    TalentExamNotificationResponseSchema,
    TalentExamStatsSchema,
    RegistrationAnalyticsSchema
)

__all__ = [
    # Auth schemas
    "UserRegisterSchema",
    "UserLoginSchema",
    "TokenResponseSchema",
    "RefreshTokenSchema",
    "PasswordResetRequestSchema",
    "PasswordResetSchema",
    "PasswordChangeSchema",
    "EmailVerificationSchema",
    "TwoFactorSetupSchema",
    "TwoFactorDisableSchema",
    "UserResponseSchema",
    "UserProfileResponseSchema",
    "UserSessionResponseSchema",
    "PasswordStrengthResponseSchema",
    "AuthStatusResponseSchema",
    "LogoutResponseSchema",
    "BulkUserRegistrationSchema",
    "UserUpdateSchema",
    "DeviceInfoSchema",
    "SecurityEventSchema",

    # Question schemas
    "QuestionGenerationRequestSchema",
    "QuestionCreateSchema",
    "QuestionUpdateSchema",
    "QuestionResponseSchema",
    "QuestionGenerationResponseSchema",
    "SubjectCreateSchema",
    "SubjectResponseSchema",
    "TopicCreateSchema",
    "TopicResponseSchema",
    "QuestionBankCreateSchema",
    "QuestionBankResponseSchema",
    "QuestionFeedbackSchema",
    "QuestionSearchSchema",
    "QuestionSearchResponseSchema",
    "AIGenerationStatsSchema",
    "BulkQuestionImportSchema",
    "QuestionValidationSchema",
    "QuestionValidationResponseSchema",

    # Certificate schemas
    "CertificateTemplateCreateSchema",
    "CertificateTemplateUpdateSchema",
    "CertificateTemplateResponseSchema",
    "CertificateCreateSchema",
    "CertificateBulkCreateSchema",
    "CertificateUpdateSchema",
    "CertificateResponseSchema",
    "CertificateVerificationSchema",
    "CertificateVerificationResponseSchema",
    "CertificateGenerationRequestSchema",
    "CertificateGenerationResponseSchema",
    "CertificateSearchSchema",
    "CertificateSearchResponseSchema",
    "CertificateStatsSchema",

    # Talent Exam schemas
    "TalentExamCreateSchema",
    "TalentExamUpdateSchema",
    "TalentExamResponseSchema",
    "TalentExamRegistrationCreateSchema",
    "TalentExamRegistrationUpdateSchema",
    "TalentExamRegistrationResponseSchema",
    "ExamCenterCreateSchema",
    "ExamCenterResponseSchema",
    "TalentExamSearchSchema",
    "TalentExamSearchResponseSchema",
    "TalentExamNotificationCreateSchema",
    "TalentExamNotificationResponseSchema",
    "TalentExamStatsSchema",
    "RegistrationAnalyticsSchema"
]
