# 🚀 MEDHASAKTHI GitHub to EC2 Deployment Guide

Complete guide for deploying MEDHASAKTHI from GitHub to AWS EC2 with automated CI/CD pipeline.

## 📋 Prerequisites

### 1. AWS Account Setup
- AWS account with EC2 access
- IAM user with appropriate permissions
- Key pair for EC2 access

### 2. GitHub Repository Setup
- Fork/clone the MEDHASAKTHI repository
- GitHub account with repository access
- GitHub Actions enabled

### 3. Domain Configuration
- Domain name (e.g., medhasakthi.com)
- DNS access for domain configuration

## 🏗️ Step 1: Launch EC2 Instance

### Instance Configuration
```bash
# Recommended instance specifications
Instance Type: t3.large (minimum) or t3.xlarge (recommended)
OS: Ubuntu 22.04 LTS or Amazon Linux 2
Storage: 50GB+ SSD
Security Group: Allow HTTP (80), HTTPS (443), SSH (22)
```

### Security Group Rules
```bash
# Inbound Rules
SSH (22) - Your IP
HTTP (80) - 0.0.0.0/0
HTTPS (443) - 0.0.0.0/0
Custom TCP (3000) - 0.0.0.0/0  # Grafana
Custom TCP (9090) - 0.0.0.0/0  # Prometheus
Custom TCP (5601) - 0.0.0.0/0  # Kibana
```

## 🔧 Step 2: Initial Server Setup

### Connect to EC2 Instance
```bash
# Connect via SSH
ssh -i your-key.pem ubuntu@your-ec2-public-ip

# Or for Amazon Linux
ssh -i your-key.pem ec2-user@your-ec2-public-ip
```

### Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Git and other tools
sudo apt install -y git curl wget nginx certbot python3-certbot-nginx

# Logout and login again for Docker group changes
exit
# Reconnect via SSH
```

## 📁 Step 3: Setup Application Directory

```bash
# Create application directory
sudo mkdir -p /opt/medhasakthi
sudo chown $USER:$USER /opt/medhasakthi
cd /opt/medhasakthi

# Clone repository
git clone https://github.com/YOUR_USERNAME/MEDHASAKTHI.git .

# Create necessary directories
mkdir -p {logs,backups,uploads,certificates,static}
mkdir -p database/{backups,init}
mkdir -p nginx/{ssl,logs}
mkdir -p monitoring/{grafana,prometheus}
mkdir -p logging
```

## 🔐 Step 4: Configure Environment Variables

### Create Production Environment File
```bash
# Copy example environment file
cp backend/.env.example .env

# Generate secure secrets
SECRET_KEY=$(openssl rand -hex 32)
JWT_SECRET=$(openssl rand -hex 32)
CSRF_SECRET=$(openssl rand -hex 32)
DB_PASSWORD=$(openssl rand -hex 16)
REDIS_PASSWORD=$(openssl rand -hex 16)
GRAFANA_PASSWORD=$(openssl rand -hex 12)

# Edit .env file with your values
nano .env
```

### Environment Variables Template
```env
# Database Configuration
DATABASE_URL=postgresql://medhasakthi_user:${DB_PASSWORD}@postgres:5432/medhasakthi_prod
DB_PASSWORD=your_generated_password

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_redis_password

# Security
SECRET_KEY=your_generated_secret_key
JWT_SECRET=your_generated_jwt_secret
CSRF_SECRET=your_generated_csrf_secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1
S3_BUCKET_NAME=medhasakthi-storage
S3_BACKUP_BUCKET=medhasakthi-backups

# OpenAI (Optional)
OPENAI_API_KEY=your_openai_api_key

# Monitoring
GRAFANA_PASSWORD=your_grafana_password

# Domain
DOMAIN=medhasakthi.com
EMAIL=<EMAIL>

# Environment
ENVIRONMENT=production
LOG_LEVEL=INFO
```

## 🌐 Step 5: Configure Domain and SSL

### DNS Configuration
```bash
# Point your domain to EC2 public IP
# A Record: medhasakthi.com -> YOUR_EC2_PUBLIC_IP
# A Record: www.medhasakthi.com -> YOUR_EC2_PUBLIC_IP
# A Record: api.medhasakthi.com -> YOUR_EC2_PUBLIC_IP
# A Record: admin.medhasakthi.com -> YOUR_EC2_PUBLIC_IP
# A Record: student.medhasakthi.com -> YOUR_EC2_PUBLIC_IP
```

### SSL Certificate Setup
```bash
# Stop nginx if running
sudo systemctl stop nginx

# Generate SSL certificates
sudo certbot certonly --standalone -d medhasakthi.com -d www.medhasakthi.com -d api.medhasakthi.com -d admin.medhasakthi.com -d student.medhasakthi.com --email <EMAIL> --agree-tos --non-interactive

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx" | sudo crontab -
```

## 🚀 Step 6: Deploy Application

### Initial Deployment
```bash
# Navigate to application directory
cd /opt/medhasakthi

# Build and start services
docker-compose -f docker-compose.production.yml build --no-cache
docker-compose -f docker-compose.production.yml up -d

# Wait for services to start
sleep 60

# Run database migrations
docker-compose -f docker-compose.production.yml exec backend alembic upgrade head

# Create initial admin user (optional)
docker-compose -f docker-compose.production.yml exec backend python -c "
from app.core.database import get_db
from app.services.user_service import user_service
import asyncio

async def create_admin():
    async for db in get_db():
        await user_service.create_super_admin(
            db=db,
            email='<EMAIL>',
            password='ChangeMe123!',
            full_name='Super Admin'
        )
        break

asyncio.run(create_admin())
"
```

### Configure Nginx Reverse Proxy
```bash
# Create Nginx configuration
sudo tee /etc/nginx/sites-available/medhasakthi << 'EOF'
server {
    listen 80;
    server_name medhasakthi.com www.medhasakthi.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name medhasakthi.com www.medhasakthi.com;

    ssl_certificate /etc/letsencrypt/live/medhasakthi.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/medhasakthi.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Main application
    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API endpoints
    location /api/ {
        proxy_pass http://localhost:80/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300;
    }

    # WebSocket support
    location /ws/ {
        proxy_pass http://localhost:80/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# Monitoring services
server {
    listen 443 ssl http2;
    server_name grafana.medhasakthi.com;

    ssl_certificate /etc/letsencrypt/live/medhasakthi.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/medhasakthi.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/medhasakthi /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test and restart Nginx
sudo nginx -t
sudo systemctl enable nginx
sudo systemctl restart nginx
```

## 🔄 Step 7: Setup GitHub Actions for Automated Deployment

### GitHub Secrets Configuration
Go to your GitHub repository → Settings → Secrets and variables → Actions

Add the following secrets:

```bash
# Server Access
PRODUCTION_HOST=your-ec2-public-ip
PRODUCTION_USER=ubuntu  # or ec2-user for Amazon Linux
PRODUCTION_SSH_KEY=your-private-key-content

# Optional: Staging Environment
STAGING_HOST=your-staging-server-ip
STAGING_USER=ubuntu
STAGING_SSH_KEY=your-staging-private-key

# Monitoring (Optional)
SLACK_WEBHOOK=your-slack-webhook-url
K6_CLOUD_TOKEN=your-k6-token
```

### SSH Key Setup
```bash
# On your local machine, generate SSH key for GitHub Actions
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/medhasakthi_deploy

# Copy public key to EC2 server
ssh-copy-id -i ~/.ssh/medhasakthi_deploy.pub ubuntu@your-ec2-ip

# Copy private key content to GitHub Secrets
cat ~/.ssh/medhasakthi_deploy  # Copy this to PRODUCTION_SSH_KEY secret
```

### Deployment Script for Server
```bash
# Create deployment script on EC2 server
sudo tee /opt/medhasakthi/deploy.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 Starting MEDHASAKTHI deployment..."

# Navigate to application directory
cd /opt/medhasakthi

# Pull latest changes
git pull origin main

# Stop services gracefully
docker-compose -f docker-compose.production.yml down --remove-orphans

# Pull latest images
docker-compose -f docker-compose.production.yml pull

# Build updated images
docker-compose -f docker-compose.production.yml build --no-cache

# Start services
docker-compose -f docker-compose.production.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 60

# Run database migrations
docker-compose -f docker-compose.production.yml exec -T backend alembic upgrade head

# Clean up old images
docker system prune -f

# Health check
echo "🔍 Running health checks..."
sleep 30

if curl -f https://medhasakthi.com/health >/dev/null 2>&1; then
    echo "✅ Frontend health check passed"
else
    echo "❌ Frontend health check failed"
    exit 1
fi

if curl -f https://medhasakthi.com/api/health >/dev/null 2>&1; then
    echo "✅ Backend health check passed"
else
    echo "❌ Backend health check failed"
    exit 1
fi

echo "🎉 Deployment completed successfully!"
EOF

# Make script executable
sudo chmod +x /opt/medhasakthi/deploy.sh
sudo chown $USER:$USER /opt/medhasakthi/deploy.sh
```

## 📊 Step 8: Setup Monitoring and Logging

### Configure Monitoring Access
```bash
# Access monitoring services
echo "📊 Monitoring Services:"
echo "Grafana: https://medhasakthi.com:3000 (admin/your_grafana_password)"
echo "Prometheus: https://medhasakthi.com:9090"
echo "Kibana: https://medhasakthi.com:5601"
```

### Setup Log Rotation
```bash
# Configure log rotation
sudo tee /etc/logrotate.d/medhasakthi << 'EOF'
/opt/medhasakthi/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        docker-compose -f /opt/medhasakthi/docker-compose.production.yml restart nginx
    endscript
}
EOF
```

## 🔄 Step 9: Automated Backups

### Database Backup Script
```bash
# Create backup script
sudo tee /usr/local/bin/medhasakthi-backup.sh << 'EOF'
#!/bin/bash
set -e

BACKUP_DIR="/opt/medhasakthi/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="medhasakthi_backup_${DATE}.sql"

echo "🗄️ Starting database backup..."

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create database backup
cd /opt/medhasakthi
docker-compose -f docker-compose.production.yml exec -T postgres pg_dump -U medhasakthi_user medhasakthi_prod > $BACKUP_DIR/$BACKUP_FILE

# Compress backup
gzip $BACKUP_DIR/$BACKUP_FILE

# Upload to S3 (if configured)
if [[ -n "$AWS_S3_BACKUP_BUCKET" ]]; then
    aws s3 cp $BACKUP_DIR/${BACKUP_FILE}.gz s3://$AWS_S3_BACKUP_BUCKET/database/
fi

# Keep only last 7 days of backups locally
find $BACKUP_DIR -name "medhasakthi_backup_*.sql.gz" -mtime +7 -delete

echo "✅ Backup completed: ${BACKUP_FILE}.gz"
EOF

# Make executable
sudo chmod +x /usr/local/bin/medhasakthi-backup.sh

# Add to crontab for daily backups at 2 AM
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/medhasakthi-backup.sh") | crontab -
```

## 🚀 Step 10: Final Verification and Launch

### Health Check Script
```bash
# Create comprehensive health check
tee /opt/medhasakthi/health-check.sh << 'EOF'
#!/bin/bash

echo "🔍 MEDHASAKTHI Health Check"
echo "=========================="

# Check Docker services
echo "📦 Docker Services:"
docker-compose -f docker-compose.production.yml ps

# Check disk space
echo -e "\n💾 Disk Usage:"
df -h /

# Check memory usage
echo -e "\n🧠 Memory Usage:"
free -h

# Check SSL certificate
echo -e "\n🔒 SSL Certificate:"
echo | openssl s_client -servername medhasakthi.com -connect medhasakthi.com:443 2>/dev/null | openssl x509 -noout -dates

# Check application endpoints
echo -e "\n🌐 Application Health:"
curl -s https://medhasakthi.com/health && echo " ✅ Frontend OK" || echo " ❌ Frontend Failed"
curl -s https://medhasakthi.com/api/health && echo " ✅ Backend OK" || echo " ❌ Backend Failed"

# Check database connection
echo -e "\n🗄️ Database Connection:"
docker-compose -f docker-compose.production.yml exec -T postgres pg_isready -U medhasakthi_user -d medhasakthi_prod && echo " ✅ Database OK" || echo " ❌ Database Failed"

# Check Redis connection
echo -e "\n📦 Redis Connection:"
docker-compose -f docker-compose.production.yml exec -T redis redis-cli ping && echo " ✅ Redis OK" || echo " ❌ Redis Failed"

echo -e "\n🎉 Health check completed!"
EOF

chmod +x /opt/medhasakthi/health-check.sh
```

### Launch Verification
```bash
# Run final health check
cd /opt/medhasakthi
./health-check.sh

# Test deployment script
./deploy.sh

# Verify all services are running
docker-compose -f docker-compose.production.yml ps

# Check logs for any errors
docker-compose -f docker-compose.production.yml logs --tail=50
```

## 🎯 Step 11: Post-Deployment Configuration

### Access Your Application
- **Main Site**: https://medhasakthi.com
- **API Documentation**: https://medhasakthi.com/api/docs
- **Admin Panel**: https://medhasakthi.com/admin
- **Student Portal**: https://medhasakthi.com/student
- **Institute Portal**: https://medhasakthi.com/institute

### Monitoring Dashboards
- **Grafana**: https://medhasakthi.com:3000
- **Prometheus**: https://medhasakthi.com:9090
- **Kibana**: https://medhasakthi.com:5601

### Default Credentials
```bash
# Super Admin (Change immediately)
Email: <EMAIL>
Password: ChangeMe123!

# Grafana
Username: admin
Password: your_grafana_password
```

## 🔧 Troubleshooting

### Common Issues and Solutions

1. **SSL Certificate Issues**
```bash
# Regenerate SSL certificate
sudo certbot delete --cert-name medhasakthi.com
sudo certbot certonly --standalone -d medhasakthi.com -d www.medhasakthi.com
```

2. **Docker Service Issues**
```bash
# Restart all services
docker-compose -f docker-compose.production.yml restart

# Check service logs
docker-compose -f docker-compose.production.yml logs service-name
```

3. **Database Connection Issues**
```bash
# Reset database
docker-compose -f docker-compose.production.yml down
docker volume rm medhasakthi_postgres_data
docker-compose -f docker-compose.production.yml up -d
```

4. **GitHub Actions Deployment Failures**
```bash
# Check SSH connection
ssh -i ~/.ssh/medhasakthi_deploy ubuntu@your-ec2-ip

# Verify deployment script permissions
ls -la /opt/medhasakthi/deploy.sh
```

## 📈 Scaling and Optimization

### Auto-Scaling Setup
- Configure AWS Auto Scaling Groups
- Set up Application Load Balancer
- Implement blue-green deployments
- Add CDN (CloudFront) for static assets

### Performance Optimization
- Enable Redis caching
- Configure database connection pooling
- Implement API rate limiting
- Set up monitoring alerts

## 🎉 Congratulations!

Your MEDHASAKTHI application is now successfully deployed on AWS EC2 with:
- ✅ Automated CI/CD pipeline from GitHub
- ✅ SSL certificates and security headers
- ✅ Load balancing and monitoring
- ✅ Automated backups and health checks
- ✅ Production-ready configuration

The application will automatically deploy when you push changes to the main branch of your GitHub repository!
